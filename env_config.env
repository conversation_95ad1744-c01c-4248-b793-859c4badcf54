# Redis 集群配置
REDIS_HOST_1=*************
REDIS_PORT_1=6001
REDIS_HOST_2=*************
REDIS_PORT_2=6001
REDIS_HOST_3=************
REDIS_PORT_3=6001
# REDIS_PASSWORD=test123  # 如果Redis没有密码，请注释掉这行
REDIS_SOCKET_TIMEOUT=3
REDIS_SOCKET_KEEPALIVE=true

# SVN 服务器配置
SVN_URL=yexiao.fun
SVN_TIMEOUT=300

# API 服务配置
API_HOST=0.0.0.0
API_PORT=5000
API_RELOAD=true

# 文件路径配置
LOG_DIRECTORY=G:/project/redis/log

# 写入缓冲区大小（一次写入的日志数量）
BATCH_SIZE=1000

# 日志配置
LOG_LEVEL=INFO

# 部署环境标识
ENVIRONMENT=development 