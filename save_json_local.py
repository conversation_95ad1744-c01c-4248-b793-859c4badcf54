import os
import re
import redis
from redis.cluster import RedisCluster
from redis.cluster import ClusterNode

from redis.commands.json.path import Path
# 移除redisearch依赖
# from redisearch import Client, TextField, NumericField, TagField
# from redisearch import IndexDefinition
# from redisearch.client import IndexType
# import redisearch
import get_info_xml as xml_parser  # 改为更有意义的名称
from config import config

# Define the log directory path
log_directory = config.LOG_DIRECTORY

# Define the regex pattern to match log content
log_pattern = re.compile(r'(\d{8}-\d{6}),\d+<(\w+)\s*:\d+>:\s*(.*)')

try:
    # Create a RedisCluster client using config
    redis_client_config = config.get_redis_client_config()
    redis_client = RedisCluster(**redis_client_config)

    redis_client.ping()
    print("Connected to Redis")
    print(f"Redis集群节点: {[f'{node.host}:{node.port}' for node in config.get_redis_startup_nodes()]}")
except redis.ConnectionError as e:
    print(f"Could not connect to Redis: {e}")



def get_max_version():
    """使用Sorted Set快速获取当前数据库中最大的version值"""
    try:
        # 使用带hash tag的key以适配Redis Cluster
        version_key = "all_items_latest_version{cluster}"
        # 使用ZREVRANGE获取最大版本号，时间复杂度O(1)
        result = redis_client.zrevrange(version_key, 0, 0, withscores=True)
        if result:
            max_version = int(result[0][1])  # 获取最大分数(version)
            print(f"📊 当前数据库中最大的version: {max_version}")
            return max_version
        
        print("📊 数据库中没有找到数据，从0开始")
        return 0
        
    except Exception as e:
        print(f"⚠️  查询最大version失败: {e}")
        print("📊 默认从0开始")
        return 0

def get_max_index_by_version():
    """获取当前数据库中最大的索引值，用于确定新数据的存储位置"""
    try:
        # 使用带hash tag的key以适配Redis Cluster
        version_key = "all_items_latest_version{cluster}"
        # 使用ZREVRANGE获取最大版本号，时间复杂度O(1)
        result = redis_client.zrevrange(version_key, 0, 0, withscores=True)
        if result:
            # 从key中获取索引号，格式为"log_json_v1:索引号"
            max_index = int(result[0][0].split(':')[1])
            print(f"📊 当前数据库中最大的索引号: {max_index}")
            return max_index + 1  # 返回下一个可用的索引号
        
        print("📊 数据库中没有找到数据，从0开始")
        return 0
        
    except Exception as e:
        print(f"⚠️  查询最大索引号失败: {e}")
        print("📊 默认从0开始")
        return 0

# 新增原子化操作函数
def atomic_add_log_with_version(redis_key, data, version):
    """原子化添加日志数据并更新版本号索引 - 适配Redis Cluster"""
    try:
        # 为Redis Cluster添加hash tag，确保数据在同一节点
        # 使用固定的hash tag确保版本索引和数据在同一节点
        version_key = "all_items_latest_version{cluster}"
        
        # 直接使用pipeline进行批量操作，不使用WATCH
        with redis_client.pipeline() as pipe:
            # 1. 添加JSON数据
            pipe.json().set(redis_key, Path.root_path(), data)
            # 2. 更新版本号到Sorted Set，使用Redis key作为member，version作为score
            pipe.zadd(version_key, {redis_key: version})
            # 执行操作
            pipe.execute()
            
    except Exception as e:
        print(f"❌ 原子化操作失败: {e}")
        # 如果pipeline失败，尝试单独执行
        try:
            redis_client.json().set(redis_key, Path.root_path(), data)
            redis_client.zadd("all_items_latest_version{cluster}", {redis_key: version})
        except Exception as e2:
            print(f"❌ 单独操作也失败: {e2}")

def initialize_version_index():
    """初始化版本号索引，从现有数据中重建Sorted Set"""
    print("🔧 正在初始化版本号索引...")
    
    try:
        # 使用带hash tag的key以适配Redis Cluster
        version_key = "all_items_latest_version{cluster}"
        
        # 检查是否已有版本号索引
        count = redis_client.zcard(version_key)
        if count > 0:
            print(f"📊 版本号索引已存在，包含 {count} 条记录")
            return
        
        # 从现有数据重建索引
        print("🔄 正在从现有数据重建版本号索引...")
        cursor = 0
        rebuilt_count = 0
        
        while True:
            # 扫描所有log_json_v1:*键
            cursor, keys = redis_client.scan(cursor, match="log_json_v1:*", count=1000)
            
            if keys:
                # 批量获取版本号并添加到Sorted Set
                version_mapping = {}
                for key in keys:
                    try:
                        # 从JSON数据中获取版本号
                        version = redis_client.json().get(key, "$.version")
                        if version and len(version) > 0:
                            version_mapping[key] = int(version[0])
                    except Exception as e:
                        # 如果无法获取版本号，从key中提取index作为版本号
                        try:
                            index = int(key.split(':')[1])
                            version_mapping[key] = index
                        except:
                            print(f"⚠️  无法处理键: {key}, 错误: {e}")
                            continue
                
                # 批量添加到Sorted Set
                if version_mapping:
                    redis_client.zadd(version_key, version_mapping)
                    rebuilt_count += len(version_mapping)
            
            if cursor == 0:
                break
        
        print(f"✅ 版本号索引重建完成，共处理 {rebuilt_count} 条记录")
        
    except Exception as e:
        print(f"❌ 初始化版本号索引失败: {e}")

# Function to process a single XML file and store it in Redis
def process_xml_file(xml_file_path):
    try:
        # 获取起始index
        start_index = get_max_index_by_version()
        print(f"🚀 开始处理XML文件，起始index: {start_index}")
        
        current_index = start_index
        datas = xml_parser.getinfo(xml_file_path)  # 使用新的导入名称
        
        print(f"Processing XML file: {xml_file_path}")
        
        # 批量处理以提高性能，但保证每批的原子性
        batch_size = config.BATCH_SIZE
        batch_data = []
        
        # 直接遍历扁平化的数据列表
        for data in datas:
            redis_key = f"log_json_v1:{current_index}"
            
            batch_data.append((redis_key, data, current_index))
            current_index += 1

            if data['version'] == 6555:
                print(redis_key)
            
            # 当批次达到指定大小时，执行批量原子操作
            if len(batch_data) >= batch_size:
                execute_batch_atomic(batch_data)
                batch_data = []
        
        # 处理剩余的数据
        if batch_data:
            execute_batch_atomic(batch_data)
        
        total_processed = current_index - start_index
        print(f"✅ Successfully processed {total_processed} log entries from {xml_file_path}")
        print(f"📈 Index范围: {start_index} ~ {current_index - 1}")
        
    except Exception as e:
        print(f"❌ 处理XML文件时发生错误: {e}")
        raise  # 重新抛出异常以便上层处理

def execute_batch_atomic(batch_data):
    """批量执行原子化操作 - 适配Redis Cluster"""
    try:
        # 使用带hash tag的key以适配Redis Cluster
        version_key = "all_items_latest_version{cluster}"
        
        # 直接使用pipeline，不使用事务
        with redis_client.pipeline() as pipe:
            # 批量添加数据
            version_mapping = {}
            for redis_key, data, count in batch_data:
                version = data['version']
                # 1. 添加JSON数据
                pipe.json().set(redis_key, Path.root_path(), data)
                # 2. 准备版本号映射，使用Redis key作为member，version作为score
                version_mapping[redis_key] = version
            
            # 3. 批量更新版本号到Sorted Set
            if version_mapping:
                pipe.zadd(version_key, version_mapping)
            
            # 执行操作
            pipe.execute()
            print(f"✅ 批量处理完成: {len(batch_data)} 条记录")
            
    except Exception as e:
        print(f"❌ 批量操作失败: {e}")
        # 如果批量操作失败，降级为单条操作
        print("🔄 降级为单条操作...")
        for redis_key, data, version in batch_data:
            atomic_add_log_with_version(redis_key, data, version)

# Function to process all XML files in a directory
def process_all_xml_files(directory):
    xml_files_found = 0
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.xml'):  # 查找XML文件而不是log文件
                xml_file_path = os.path.join(root, file)
                xml_files_found += 1
                print(f"Found XML file {xml_files_found}: {xml_file_path}")
                process_xml_file(xml_file_path)
    
    if xml_files_found == 0:
        print(f"No XML files found in directory: {directory}")
        # 如果没有找到XML文件，尝试处理当前目录的res.xml
        if os.path.exists("res.xml"):
            print("Processing default res.xml in current directory")
            process_xml_file("res.xml")
        else:
            print("No res.xml found in current directory either")
    else:
        print(f"Total XML files processed: {xml_files_found}")

def clean_version_index():
    """清理版本号索引中的无效条目"""
    print("🧹 正在清理版本号索引...")
    
    try:
        # 使用带hash tag的key以适配Redis Cluster
        version_key = "all_items_latest_version{cluster}"
        
        # 获取所有版本号索引条目
        all_items = redis_client.zrange(version_key, 0, -1)
        invalid_keys = []
        
        for key in all_items:
            # 检查对应的JSON数据是否存在
            if not redis_client.exists(key):
                invalid_keys.append(key)
        
        # 删除无效条目
        if invalid_keys:
            redis_client.zrem(version_key, *invalid_keys)
            print(f"🗑️  清理了 {len(invalid_keys)} 个无效条目")
        else:
            print("✅ 版本号索引无需清理")
            
    except Exception as e:
        print(f"❌ 清理版本号索引失败: {e}")

def create_index():
    """使用Redis 7.0+原生搜索功能创建索引"""
    index_name = 'my_index'
    
    # 删除现有索引（如果存在）
    try:
        redis_client.execute_command('FT.DROPINDEX', index_name)
        print(f"Dropped existing index: {index_name}")
    except Exception as e:
        print(f"Index {index_name} does not exist or could not be dropped: {e}")
    
    # 使用Redis原生FT.CREATE命令创建索引
    try:
        # 构建索引创建命令
        create_cmd = [
            'FT.CREATE', index_name,
            'ON', 'JSON',
            'PREFIX', '1', 'log_json_v1:',
            'LANGUAGE', 'chinese',
            'SCHEMA',
            '$.version', 'AS', 'version', 'NUMERIC',  # 版本号用NUMERIC更适合聚合
            '$.time', 'AS', 'time', 'NUMERIC',  # 将time改为NUMERIC类型，存储时间戳
            '$.actions', 'AS', 'actions', 'TAG',
            '$.filelist', 'AS', 'filelist', 'TEXT',
            '$.msg', 'AS', 'msg', 'TEXT',
            '$.exist', 'AS', 'exist', 'TAG',  # 添加exist字段，用TAG类型便于筛选
            '$.action', 'AS', 'action', 'TAG'  # 添加action字段，也很有用
        ]
        
        result = redis_client.execute_command(*create_cmd)
        print(f"Index created successfully: {result}")
        
        # 测试搜索功能
        search_result = redis_client.execute_command('FT.SEARCH', index_name, '*', 'LIMIT', '0', '5')
        print(f"Test search result: {search_result}")
        
    except Exception as e:
        print(f"Error creating index: {e}")

def search_logs(query, limit=10):
    """使用Redis原生搜索功能搜索日志"""
    index_name = 'my_index'
    try:
        # 使用FT.SEARCH命令搜索
        result = redis_client.execute_command(
            'FT.SEARCH', index_name, query, 
            'LIMIT', '0', str(limit)
        )
        
        # 格式化搜索结果
        if len(result) > 0:
            total_results = result[0]
            print(f"Found {total_results} results:")
            
            # 解析搜索结果
            documents = []
            for i in range(1, len(result), 2):
                if i + 1 < len(result):
                    doc_id = result[i]
                    doc_fields = result[i + 1]
                    documents.append({
                        'id': doc_id,
                        'fields': doc_fields
                    })
            
            return documents
        else:
            print("No results found")
            return []
            
    except Exception as e:
        print(f"Search error: {e}")
        return []

def search_by_version(version):
    """根据版本号搜索日志"""
    query = f"@version:{version}"
    return search_logs(query)

def search_by_keyword(keyword):
    """根据关键词搜索日志内容"""
    query = f"@msg:{keyword}"
    return search_logs(query)

# 主程序流程
if __name__ == "__main__":
    
    print("🔧 开始初始化版本号索引...")
    initialize_version_index()
    
    print("\n🔧 开始创建/更新搜索索引...")
    create_index()
    
    print("\n📁 开始处理XML文件...")
    # Process all log files in the specified directory
    process_all_xml_files(log_directory)
    
    # print("\n🧹 清理版本号索引...")
    # clean_version_index()
    
    print("\n🎉 所有处理完成！")
    
    # 示例搜索功能测试
    # print("\n=== 搜索测试 ===")
    # search_logs("*")  # 搜索所有文档

