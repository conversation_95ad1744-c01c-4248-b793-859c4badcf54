import os
import subprocess
import tempfile
from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import JSONResponse
from datetime import datetime
import logging
from typing import Optional, Dict, Any

# 导入现有的Redis连接和处理函数
from save_json_local import (
    redis_client, 
    get_max_version,  # 恢复使用get_max_version
    process_xml_file, 
    create_index
)
# 导入配置
from config import config

# 配置日志
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL))
logger = logging.getLogger(__name__)

app = FastAPI(
    title="SVN Log Writer API",
    description="提供SVN日志自动获取和写入Redis的API服务",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

def execute_svn_command(start_version, svn_url, output_file):
    """
    执行SVN log命令获取指定版本范围的日志
    
    Args:
        start_version: 起始版本号(当前最新版本+1)
        svn_url: SVN仓库URL
        output_file: 输出XML文件路径
    
    Returns:
        bool: 执行是否成功
    """
    try:
        # 构建SVN命令: svn log url -r start_version:HEAD -v --xml output_file 
        # 从start_version到HEAD获取所有增量数据
        cmd = [
            'svn', 'log',
            svn_url,
            f'-r', f'{start_version}:HEAD',
            '-v',
            '--xml'
        ]
        
        logger.info(f"执行SVN命令: {' '.join(cmd)}")
        
        # 执行SVN命令并捕获输出
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            encoding='utf-8',
            text=True, 
            timeout=config.SVN_TIMEOUT  # 使用配置的超时时间
        )
        
        if result.returncode == 0:
            # 将输出写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result.stdout)
            logger.info(f"SVN命令执行成功，输出已保存到: {output_file}")
            return True, None
        else:
            error_msg = result.stderr
            logger.error(f"SVN命令执行失败: {error_msg}")
            
            # 检查是否是版本号不存在的错误
            if "No such revision" in error_msg:
                return False, "version_not_found"
            
            return False, error_msg
            
    except subprocess.TimeoutExpired:
        logger.error("SVN命令执行超时")
        return False, "timeout"
    except Exception as e:
        logger.error(f"执行SVN命令时发生错误: {e}")
        return False, str(e)

@app.post("/update_log")
async def update_log(url: str) -> Dict[str, Any]:
    """
    自动写入增量log数据的API端点
    
    功能：
    1. 自动查询Redis中最大的版本号
    2. 从最大版本号+1开始获取SVN增量数据
    3. 解析并写入Redis数据库
    
    Returns:
        JSON响应包含处理结果
    """
    try:
        # 使用配置的SVN URL
        if url is None:
            svn_url = config.SVN_URL
        else:
            svn_url = url
        
        # 1. 查询当前记录中最新的log version
        logger.info("🔍 开始查询Redis中最新的log version...")
        latest_version = get_max_version()  # 使用get_max_version获取实际的SVN版本号
        latest_version = 5306
        start_version = latest_version + 1 if latest_version > 0 else 1
        
        logger.info(f"📊 当前Redis中最新version: {latest_version}")
        logger.info(f"🚀 将从version {start_version} 开始获取增量数据")
        
        # 2. 创建临时XML文件（使用相对路径）
        xml_file_path = f"svn_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml"
        
        # 3. 执行SVN命令获取增量日志
        logger.info(f"🚀 开始执行SVN命令获取增量日志...")
        logger.info(f"📝 SVN URL: {svn_url}")
        logger.info(f"📅 版本范围: {start_version}:HEAD")
        
        svn_success, error_msg = execute_svn_command(start_version, svn_url, xml_file_path)
        
        if not svn_success:
            # 清理临时文件
            if os.path.exists(xml_file_path):
                os.unlink(xml_file_path)
            
            # 如果是版本号不存在的错误，返回200状态码
            if error_msg == "version_not_found":
                return {
                    'success': True,
                    'message': f'版本 {start_version} 不存在，现在可能已经是最新版本',
                    'latest_version': latest_version,
                    'requested_version': start_version,
                    'processed_count': 0
                }
            
            # 其他错误返回500
            raise HTTPException(
                status_code=500,
                detail={
                    'success': False,
                    'error': error_msg or 'SVN命令执行失败',
                    'latest_version': latest_version
                }
            )
        
        # 检查XML文件是否生成且有内容
        if not os.path.exists(xml_file_path) or os.path.getsize(xml_file_path) == 0:
            logger.warning("⚠️ XML文件未生成或为空，可能没有新的日志记录")
            if os.path.exists(xml_file_path):
                os.unlink(xml_file_path)
            return {
                'success': True,
                'message': '没有新的日志记录需要处理',
                'latest_version': latest_version,
                'processed_count': 0
            }
            
        # 检查SVN日志中的最大版本号
        try:
            import xml.etree.ElementTree as ET
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            # 获取所有logentry的revision属性
            revisions = [int(entry.get('revision')) for entry in root.findall('logentry')]
            if revisions:
                max_svn_version = max(revisions)
                logger.info(f"📊 SVN日志中最大版本号: {max_svn_version}")
                
                if max_svn_version <= latest_version:
                    logger.info("✅ 当前数据库版本已是最新，无需更新")
                    os.unlink(xml_file_path)
                    return {
                        'success': True,
                        'message': '当前数据库版本已是最新',
                        'latest_version': latest_version,
                        'svn_max_version': max_svn_version,
                        'processed_count': 0
                    }
            else:
                logger.warning("⚠️ SVN日志中未找到版本信息")
        except Exception as e:
            logger.error(f"❌ 解析SVN日志版本号时出错: {e}")
            # 继续处理，因为解析错误不应该影响正常流程
        
        # 4. 处理XML文件并写入Redis
        logger.info(f"📄 开始处理XML文件: {xml_file_path}")
        
        # 记录处理前的状态
        before_processing = get_max_version()
        
        # 处理XML文件
        process_xml_file(xml_file_path)
        
        # 记录处理后的状态
        after_processing = get_max_version()
        processed_count = after_processing - before_processing
        
        # 5. 清理临时文件
        try:
            os.unlink(xml_file_path)
            logger.info(f"🗑️ 临时文件已清理: {xml_file_path}")
        except Exception as e:
            logger.warning(f"清理临时文件失败: {e}")
        
        # 6. 返回处理结果
        return {
            'success': True,
            'message': 'Log写入成功完成',
            'latest_version_before': before_processing,
            'latest_version_after': after_processing,
            'processed_count': processed_count,
            'svn_url': svn_url,
            'start_version': start_version,
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ 处理log写入时发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': f'服务器内部错误: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
        )

@app.get("/status")
async def status() -> Dict[str, Any]:
    """
    获取系统状态和当前最新版本
    """
    try:
        # 测试Redis连接
        redis_client.ping()
        redis_status = "connected"
        
        # 获取当前最新版本
        latest_version = get_max_version()
        
        return {
            'success': True,
            'redis_status': redis_status,
            'latest_version': latest_version,
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail={
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
        )

@app.get("/")
async def home() -> Dict[str, Any]:
    """
    API服务主页和基本信息
    """
    return {
        'service': 'SVN Log Writer API',
        'version': '2.0.0',
        'framework': 'FastAPI',
        'endpoints': {
            'write_log': '/write_log (自动获取增量数据)',
            'status': '/status',
            'create_index': '/create_index',
            'home': '/',
            'docs': '/docs',
            'redoc': '/redoc'
        },
        'description': '提供SVN日志自动获取和写入Redis的API服务'
    }

if __name__ == '__main__':
    import uvicorn
    
    # 在启动时检查连接状态
    try:
        redis_client.ping()
        logger.info("✅ Redis连接正常")
    except Exception as e:
        logger.error(f"❌ Redis连接失败: {e}")
    
    # 启动FastAPI应用
    logger.info("🚀 启动API服务...")
    config.print_config()  # 打印配置信息
    uvicorn.run(
        "api_service:app", 
        host=config.API_HOST, 
        port=config.API_PORT, 
        reload=config.API_RELOAD
    ) 